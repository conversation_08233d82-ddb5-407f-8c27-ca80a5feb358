/* auto-generated by NAPI-RS */
/* eslint-disable */

export class Compiler {
  constructor()
}
export type JsCompiler = Compiler

export declare function analyze(src: string, options: Buffer, signal?: AbortSignal | undefined | null): Promise<string>

export declare function bundle(confItems: Buffer, signal?: AbortSignal | undefined | null): Promise<{ [index: string]: { code: string, map?: string } }>

export declare function getTargetTriple(): string

export declare function initCustomTraceSubscriber(traceOutFilePath?: string | undefined | null): void

export declare function minify(code: <PERSON>uff<PERSON>, opts: Buffer, isJson: boolean, extras: NapiMinifyExtra, signal?: AbortSignal | undefined | null): Promise<TransformOutput>

export declare function minifySync(code: <PERSON>uff<PERSON>, opts: Buffer, isJson: boolean, extras: NapiMinifyExtra): TransformOutput

export interface NapiMinifyExtra {
  mangleNameCache?: object
}

export declare function newMangleNameCache(): object

export declare function parse(src: Buffer | string, options: Buffer, filename?: string | undefined | null, signal?: AbortSignal | undefined | null): Promise<string>

export declare function parseFile(path: string, options: Buffer, signal?: AbortSignal | undefined | null): Promise<string>

export declare function parseFileSync(path: string, opts: Buffer): string

export declare function parseSync(src: Buffer | string, opts: Buffer, filename?: string | undefined | null): string

export declare function print(programJson: string, options: Buffer, signal?: AbortSignal | undefined | null): Promise<TransformOutput>

export declare function printSync(program: string, options: Buffer): TransformOutput

export declare function transform(src: string, isModule: boolean, options: Buffer, signal?: AbortSignal | undefined | null): Promise<TransformOutput>

export declare function transformFile(src: string, isModule: boolean, options: Buffer, signal?: AbortSignal | undefined | null): Promise<TransformOutput>

export declare function transformFileSync(s: string, isModule: boolean, opts: Buffer): TransformOutput

export interface TransformOutput {
  code: string
  map?: string
  output?: string
  diagnostics: Array<string>
}

/** Hack for `Type Generation` */
export interface TransformOutput {
  code: string
  map?: string
}

export declare function transformSync(s: string, isModule: boolean, opts: Buffer): TransformOutput

