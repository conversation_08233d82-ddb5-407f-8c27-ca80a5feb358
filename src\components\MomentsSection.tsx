import button from "../images/HeroSection/Group 3.svg";
import imgOne from "../images/MomentsSection/Mask group (3).svg";
import img2 from "../images/MomentsSection/Mask group (4).svg";
import img3 from "../images/MomentsSection/Mask group (5).svg";
const MomentsSection = () => {
  return (
    <section className="py-28 px-20 bg-gray-50 ">
      <div className="max-w-7xl mx-auto">
        <div className=" gap-16 items-start ">
          <div className="space-y-6 pt-8 max-w-xl">
            <h2 className="text-4xl font-bold text-gray-900">
              Celebrating Your Most
              <br />
              Important Moments
            </h2>
            <p className="text-gray-600 leading-relaxed">
              We're here to celebrate life's most important moments with you.
              Whether it's the beginning of forever or the celebration of a
              lifetime, we want to be there to capture every single moment.
            </p>
            {/* <button className="bg-purple-600 hover:bg-purple-700 text-white px-8 py-3 rounded-full text-sm font-medium transition-colors">
              VIEW WORKS
            </button> */}
            <button className="bg-transparent border-none p-0">
              {" "}
              <img src={button} alt="btn" className="w-auto h-20 " />
            </button>
          </div>

          <div className="relative img-containter mt-10 justify-start flex gap-x-28">
            <div className="mt-10">
              <img
                //src="https://images.unsplash.com/photo-1494790108755-2616c0763692?w=200&h=280&fit=crop"
                src={imgOne}
                alt="Portrait 1"
                className="w-auto h-96 object-cover rounded-lg msImg"
              />
              <div className=" mb-2">
                <div className="text-2xl font-bold text-gray-900">01.</div>
              </div>
            </div>

            <div className="msImg2Con">
              <img
                // src="https://images.unsplash.com/photo-1531123897727-8f129e1688ce?w=200&h=280&fit=crop"
                src={img2}
                alt="Portrait 2"
                className="w-auto h-96 object-cover rounded-lg msImg"
              />
              <div className=" mb-2">
                <div className="text-2xl font-bold text-gray-900">02.</div>
              </div>
            </div>

            <div className="msImg3Con">
              <img
                //src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=160&h=240&fit=crop"
                src={img3}
                alt="Portrait 3"
                className="w-auto h-96 object-cover rounded-lg msImg"
              />
              <div className=" mb-2">
                <div className="text-2xl font-bold text-gray-900">03.</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default MomentsSection;
