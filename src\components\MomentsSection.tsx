import button from "../images/HeroSection/Group 3.svg";

const MomentsSection = () => {
  return (
    <section className="py-16 px-20 bg-gray-50">
      <div className="max-w-7xl mx-auto">
        <div className=" gap-16 items-start">
          <div className="space-y-6 pt-8 max-w-xl">
            <h2 className="text-4xl font-bold text-gray-900">
              Celebrating Your Most
              <br />
              Important Moments
            </h2>
            <p className="text-gray-600 leading-relaxed">
              We're here to celebrate life's most important moments with you.
              Whether it's the beginning of forever or the celebration of a
              lifetime, we want to be there to capture every single moment.
            </p>
            {/* <button className="bg-purple-600 hover:bg-purple-700 text-white px-8 py-3 rounded-full text-sm font-medium transition-colors">
              VIEW WORKS
            </button> */}
            <button className="bg-transparent border-none p-0">
              {" "}
              <img src={button} alt="btn" className="w-auto h-16 " />
            </button>
          </div>
          <div className="relative h-96">
            <div className="absolute top-0 left-0">
              <div className="text-right mb-2">
                <div className="text-2xl font-bold text-gray-900">01.</div>
              </div>
              <img
                src="https://images.unsplash.com/photo-1494790108755-2616c0763692?w=200&h=280&fit=crop"
                alt="Portrait 1"
                className="w-40 h-56 object-cover rounded-lg"
              />
            </div>

            {/* Image 02 - center right, offset down */}
            <div className="absolute top-16 right-0">
              <div className="text-left mb-2">
                <div className="text-2xl font-bold text-gray-900">02.</div>
              </div>
              <img
                src="https://images.unsplash.com/photo-1531123897727-8f129e1688ce?w=200&h=280&fit=crop"
                alt="Portrait 2"
                className="w-40 h-56 object-cover rounded-lg"
              />
            </div>

            {/* Image 03 - bottom center */}
            <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2">
              <div className="text-center mb-2">
                <div className="text-2xl font-bold text-gray-900">03.</div>
              </div>
              <img
                src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=160&h=240&fit=crop"
                alt="Portrait 3"
                className="w-32 h-48 object-cover rounded-lg"
              />
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default MomentsSection;
