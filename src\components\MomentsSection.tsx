import button from "../images/HeroSection/Group 3.svg";
import imgOne from "../images/MomentsSection/Mask group (3).svg";
import img2 from "../images/MomentsSection/Mask group (4).svg";
import img3 from "../images/MomentsSection/Mask group (5).svg";
const MomentsSection = () => {
  return (
    <section className="py-12 sm:py-16 lg:py-28 px-4 sm:px-6 lg:px-20 bg-gray-50">
      <div className="max-w-7xl mx-auto">
        <div className="space-y-8 sm:space-y-12 lg:space-y-16">
          <div className="space-y-6 max-w-xl">
            <h2 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-900 text-left">
              <span className="block sm:hidden">
                Celebrating Your Most Important Moments
              </span>
              <span className="hidden sm:block">
                Celebrating Your Most
                <br />
                Important Moments
              </span>
            </h2>
            <p className="text-sm sm:text-base text-gray-600 leading-relaxed text-left">
              We're here to celebrate life's most important moments with you.
              Whether it's the beginning of forever or the celebration of a
              lifetime, we want to be there to capture every single moment.
            </p>
            <div className="flex justify-start">
              <button type="button" className="bg-transparent border-none p-0">
                <img
                  src={button}
                  alt="btn"
                  className="w-auto h-12 sm:h-16 lg:h-20"
                />
              </button>
            </div>
          </div>

          {/* Mobile: Single column grid, Tablet/Desktop: Three columns */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8 lg:gap-12">
            <div className="space-y-4">
              <img
                src={imgOne}
                alt="Portrait 1"
                className="w-full h-64 sm:h-80 lg:h-96 object-cover rounded-lg"
              />
              <div className="text-xl sm:text-2xl font-bold text-gray-900 text-left">
                01.
              </div>
            </div>

            <div className="space-y-4">
              <img
                src={img2}
                alt="Portrait 2"
                className="w-full h-64 sm:h-80 lg:h-96 object-cover rounded-lg"
              />
              <div className="text-xl sm:text-2xl font-bold text-gray-900 text-left">
                02.
              </div>
            </div>

            <div className="space-y-4 sm:col-span-2 lg:col-span-1">
              <img
                src={img3}
                alt="Portrait 3"
                className="w-full h-64 sm:h-80 lg:h-96 object-cover rounded-lg"
              />
              <div className="text-xl sm:text-2xl font-bold text-gray-900 text-left">
                03.
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default MomentsSection;
