import { useState, useEffect } from "react";
import { useLocation } from "react-router-dom";
import Header from "./Header";
import Footer from "./Footer";

const PricingPage = () => {
  const location = useLocation();
  const [activeCategory, setActiveCategory] = useState("prewedding");
  const [isMobileSidebarOpen, setIsMobileSidebarOpen] = useState(false);

  useEffect(() => {
    window.scrollTo(0, 0);
    const timer = setTimeout(() => {
      window.scrollTo(0, 0);
      document.body.style.display = "none";
      document.body.offsetHeight;
      document.body.style.display = "";
    }, 10);
    return () => clearTimeout(timer);
  }, [location.pathname]);

  const handleWhatsAppNav = () => {
    const phoneNumber = "+2349027104215";
    const message =
      "Hi there!!! Mr <PERSON><PERSON>. \nI would like to learn more about your services.";
    window.open(
      `https://wa.me/${phoneNumber}?text=${encodeURIComponent(message)}`,
      "_blank"
    );
  };

  const toggleMobileSidebar = () => {
    setIsMobileSidebarOpen(!isMobileSidebarOpen);
  };

  const selectCategory = (categoryId: string) => {
    setActiveCategory(categoryId);
    setIsMobileSidebarOpen(false); // Close mobile sidebar after selection
  };

  const categories = [
    { id: "prewedding", name: "Pre-Wedding Photoshoot" },
    { id: "weddings", name: "Weddings" },
    { id: "introductions", name: "Introductions" },
    { id: "maternity", name: "Maternity Shoots" },
    { id: "birthday", name: "Birthday Shoots" },
    { id: "family", name: "Family Shoots" },
  ];

  const pricingData = {
    prewedding: [
      {
        title: "2 Outfit Package",
        price: "₦120,000",
        features: [
          "6 Professionally edited images",
          "Complementary wall frame",
          "Soft copies only",
        ],
      },
      {
        title: "3 Outfit Package",
        price: "₦150,000",
        features: [
          "10 Professionally edited images",
          "Creative direction/setup",
          "Complementary wall frame",
          "Soft copies only",
        ],
      },
    ],
    weddings: [
      {
        title: "SAPPHIRE PACKAGE",
        subtitle: "VIDEOGRAPHY AND PHOTOGRAPHY",
        price: "₦1,000,000",
        features: [
          "1 Drone",
          "2 full frame cameras",
          "2 photographers",
          "2 Videographers",
          "Pre wedding thriller",
          "2 wedding thriller video",
          "A 12*30 synthetic photobook with a case",
          "Calendar with a mini book",
          "14*14 Acrylic frame",
          "16*20 frame",
          "Full Wedding pictures & Video all inside two 32 Gig flash drive",
        ],
      },

      {
        title: "GOLD PACKAGE",
        subtitle: "VIDEOGRAPHY AND PHOTOGRAPHY",
        price: "₦850,000",
        features: [
          "2 full frame cameras",
          "2 photographers",
          "Pre wedding thriller",
          "1 wedding thriller video",
          "A 12*30 synthetic photobook with a case",
          "Calendar with a mini book",
          "16*20 frame",
          "Full Wedding pictures & Video all inside a 32 Gig flash drive",
        ],
      },
      {
        title: "SILVER PACKAGE",
        subtitle: "VIDEOGRAPHY AND PHOTOGRAPHY",
        price: "₦700,000",
        features: [
          "1 Full frame camera",
          "1 photographer",
          "1 Videographer",
          "1 Wedding thriller",
          "12*30 synthetic photobook with a box",
          "with a calendar 14*14 frame",
          "Full Wedding pictures in a Flash Drive",
        ],
      },
      {
        title: "SAPPHIRE PACKAGE",
        subtitle: "PHOTOGRAPHY ONLY",
        price: "₦900,000",
        features: [
          "2 full frame cameras",
          "2 photographers",
          "A 12*30 synthetic photobook with a case",
          "Calendar with a mini book",
          "14*14 Acrylic frame",
          "16*20 frame",
          "Full Wedding pictures inside one 32 Gig flash drive",
        ],
      },
      {
        title: "GOLD PACKAGE",
        subtitle: "PHOTOGRAPHY ONLY",
        price: "₦750,000",
        features: [
          "2 full frame cameras",
          "2 photographers",
          "12*30 synthetic photobook with a box",
          "A calendar",
          "16*20 frame",
          "Full Wedding pictures all in a 16Gig flash Drive",
        ],
      },

      {
        title: "SILVER PACKAGE",
        subtitle: "PHOTOGRAPHY ONLY",
        price: "₦600,000",
        features: [
          "1 Full frame camera",
          "1 photographer",
          "12*30 synthetic photobook with a box",
          "With a calendar 14*14 frame",
          "Full Wedding pictures in a Flash Drive",
        ],
      },
    ],
    introductions: [
      {
        title: "Introduction Package",
        price: "₦400,000",
        features: [
          "1 Full frame camera",
          "1 photographer",
          "1 Videographer",
          "1 Intro thriller",
          "Full pictures & Video in a 32Gig Flash Drive",
        ],
      },
    ],
    maternity: [
      {
        title: "1 Outfit Package",
        price: "₦90,000",
        features: [
          "4 Professionally edited images",
          "Creative direction/setup",
          "Soft copies only",
        ],
      },
      {
        title: "2 Outfit Package",
        price: "₦150,000",
        features: [
          "6 Professionally edited images",
          "Creative direction/setup",
          "Soft copies only",
        ],
      },
    ],
    birthday: [
      {
        title: "1 Outfit Package",
        price: "₦70,000",
        features: [
          "4 Professionally edited images",
          "Creative direction/setup",
          "Soft copies only",
        ],
      },
      {
        title: "2 Outfit Package",
        price: "₦100,000",
        features: [
          "6 Professionally edited images",
          "Creative direction/setup",
          "Soft copies only",
        ],
      },
      {
        title: "3 Outfit Package",
        price: "₦120,000",
        features: [
          "8 Professionally edited images",
          "Creative direction/setup",
          "Soft copies only",
        ],
      },
    ],
    Infant: [
      {
        title: "1 Outfit Package",
        price: "₦80,000",
        features: [
          "4 Professionally edited images",
          "Creative direction/setup with props",
          "Soft copies only",
        ],
      },
      {
        title: "2 Outfit Package",
        price: "₦130,000",
        features: [
          "6 Professionally edited images",
          "Creative direction/setup with props",
          "Soft copies only",
        ],
      },
      {
        title: "3 Outfit Package",
        price: "₦170,000",
        features: [
          "8 Professionally edited images",
          "Creative direction/setup with props",
          "Soft copies only",
        ],
      },
    ],
    family: [
      {
        title: "2 Outfit Package",
        price: "₦120,000",
        features: [
          "6 Professionally edited images",
          "Creative direction/setup",
          "Soft copies only",
          "One 14 X 14 Frame",
        ],
      },
      {
        title: "3 Outfit Package",
        price: "₦170,000",
        features: [
          "12 Professionally edited images",
          "Creative direction/setup",
          "Soft copies only",
          "One 16 X 20 Frame",
        ],
      },
    ],
  };

  return (
    <div className="min-h-screen bg-white flex flex-col">
      <Header />
      <div className="pt-20 flex-1">
        {/* Mobile Category Selector */}
        <div className="lg:hidden px-4 py-4 bg-gray-50 border-b">
          <button
            type="button"
            onClick={toggleMobileSidebar}
            className="w-full flex items-center justify-between px-4 py-3 bg-white border border-gray-300 rounded-lg text-left"
          >
            <span className="font-medium text-gray-900">
              {categories.find((cat) => cat.id === activeCategory)?.name}
            </span>
            <svg
              className={`w-5 h-5 text-gray-500 transition-transform ${
                isMobileSidebarOpen ? "rotate-180" : ""
              }`}
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M19 9l-7 7-7-7"
              />
            </svg>
          </button>

          {/* Mobile Dropdown Menu */}
          {isMobileSidebarOpen && (
            <div className="absolute left-4 right-4 mt-2 bg-white border border-gray-300 rounded-lg shadow-lg z-10">
              <nav className="py-2">
                {categories.map((category) => (
                  <button
                    key={category.id}
                    type="button"
                    onClick={() => selectCategory(category.id)}
                    className={`w-full text-left px-4 py-3 hover:bg-gray-100 transition-colors ${
                      activeCategory === category.id
                        ? "bg-violet-800 text-white hover:bg-violet-900"
                        : "text-gray-700"
                    }`}
                  >
                    {category.name}
                  </button>
                ))}
              </nav>
            </div>
          )}
        </div>

        <div className="flex">
          {/* Desktop Sidebar */}
          <div className="hidden lg:block w-64 bg-gray-50 min-h-screen p-6 fixed left-0 top-16 overflow-y-auto">
            <h2 className="text-xl font-bold text-gray-900 mb-6">Services</h2>
            <nav className="space-y-2">
              {categories.map((category) => (
                <button
                  key={category.id}
                  type="button"
                  onClick={() => setActiveCategory(category.id)}
                  className={`w-full text-left px-4 py-3 rounded-lg transition-colors ${
                    activeCategory === category.id
                      ? "bg-indigo-700 text-white"
                      : "text-gray-700 hover:bg-gray-200"
                  }`}
                >
                  {category.name}
                </button>
              ))}
            </nav>
          </div>

          {/* Main Content */}
          <div className="flex-1 lg:ml-64 p-4 sm:p-6 lg:p-12 min-h-screen lg:min-h-[calc(100vh-5rem)]">
            <div className="max-w-6xl mx-auto">
              <h1 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-3">
                {categories.find((cat) => cat.id === activeCategory)?.name}
              </h1>

              <div className="mb-6 sm:mb-8 relative">
                <div className="bg-gradient-to-r from-amber-50 to-orange-50 border-l-4 border-amber-400 rounded-r-lg p-4 sm:p-6 shadow-sm hover:shadow-md transition-all duration-300 transform hover:scale-[1.02] animate-pulse-subtle">
                  <div className="flex items-start space-x-3">
                    <div className="flex-shrink-0">
                      <svg
                        className="w-5 h-5 sm:w-6 sm:h-6 text-amber-500 animate-bounce-gentle"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path
                          fillRule="evenodd"
                          d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                          clipRule="evenodd"
                        />
                      </svg>
                    </div>
                    <div className="flex-1">
                      <h3 className="text-sm sm:text-base font-semibold text-amber-800 mb-1">
                        Important Notice
                      </h3>
                      <p className="text-xs sm:text-sm text-amber-700 font-medium">
                        All shoots outside the studio attracts an extra charge
                        of{" "}
                        <span className="font-bold text-amber-900">
                          ₦40,000
                        </span>
                      </p>
                    </div>
                  </div>
                </div>
              </div>
              <div className="grid grid-cols-1 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
                {pricingData[activeCategory as keyof typeof pricingData]?.map(
                  (package_, index) => (
                    <div
                      key={index}
                      className="rounded-lg p-6 shadow-lg"
                      style={{ backgroundColor: "rgb(44, 14, 168)" }}
                    >
                      <div className="text-center mb-6">
                        <h3 className="text-xl font-bold text-white mb-2">
                          {package_.title}
                        </h3>
                        {package_.subtitle && (
                          <p className="text-sm text-gray-200 mb-2">
                            {package_.subtitle}
                          </p>
                        )}
                        <div className="text-2xl font-bold text-white">
                          {package_.price}
                        </div>
                      </div>

                      <ul className="space-y-3 mb-6">
                        {package_.features.map((feature, featureIndex) => (
                          <li key={featureIndex} className="flex items-start">
                            <svg
                              className="w-5 h-5 text-green-400 mr-3 mt-0.5 flex-shrink-0"
                              fill="currentColor"
                              viewBox="0 0 20 20"
                            >
                              <path
                                fillRule="evenodd"
                                d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                clipRule="evenodd"
                              />
                            </svg>
                            <span className="text-white text-sm">
                              {feature}
                            </span>
                          </li>
                        ))}
                      </ul>

                      <button
                        onClick={handleWhatsAppNav}
                        className="w-full bg-white text-indigo-700 font-semibold py-3 px-4 rounded-lg hover:bg-gray-100 transition-colors"
                      >
                        Make a Reservation
                      </button>
                    </div>
                  )
                )}
              </div>
            </div>
          </div>
        </div>

        {/* <Footer /> */}
      </div>
    </div>
  );
};

export default PricingPage;
