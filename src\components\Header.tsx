import { Link } from "react-router-dom";
import btn from "../images/Frame 11.svg";

const Header = () => {
  const handleInstagramNav = () => {
    window.open("https://instagram.com/direct/new/", "_blank");
  };

  return (
    <header className="bg-white py-3 sm:py-4 px-4 sm:px-6 lg:px-20 fixed top-0 left-0 right-0 z-50 shadow-sm">
      <div className="max-w-7xl mx-auto flex justify-between items-center">
        <div className="flex items-center space-x-6 sm:space-x-8">
          <Link
            to="/"
            className="text-lg sm:text-xl font-bold text-gray-900 hover:text-violet-800 transition-colors"
          >
            <span className="hidden sm:inline">DotunAjayi Studios</span>
            <span className="sm:hidden">DA Studios</span>
          </Link>
          <nav className="hidden md:flex items-center space-x-6">
            <Link
              to="/pricing"
              className="text-sm font-medium text-gray-700 hover:text-violet-800 transition-colors"
            >
              Pricing/Services
            </Link>
            <Link
              to="/terms"
              className="text-sm font-medium text-gray-700 hover:text-violet-800 transition-colors"
            >
              Terms & Conditions
            </Link>
          </nav>
        </div>
        <a href="#" onClick={handleInstagramNav}>
          <img src={btn} alt="btn" className="w-auto h-8 sm:h-11" />
        </a>
      </div>
    </header>
  );
};

export default Header;
