import img1 from "../images/PortfolioSVGs/IMG_0033.svg";
import img2 from "../images/PortfolioSVGs/IMG_0037.svg";
import img3 from "../images/PortfolioSVGs/IMG_0038.svg";
import img4 from "../images/PortfolioSVGs/IMG_0039.svg";
import img5 from "../images/PortfolioSVGs/IMG_0069.svg";
import img6 from "../images/PortfolioSVGs/IMG_0070.svg";
import img7 from "../images/PortfolioSVGs/IMG_0072.svg";
import img8 from "../images/PortfolioSVGs/IMG_0080.svg";
import img9 from "../images/PortfolioSVGs/IMG_0081.svg";
import img10 from "../images/PortfolioSVGs/IMG_0082.svg";
import img11 from "../images/PortfolioSVGs/IMG_0217.svg";
import img12 from "../images/PortfolioSVGs/IMG_8784.svg";
import img13 from "../images/PortfolioSVGs/IMG_8785.svg";
import img14 from "../images/PortfolioSVGs/IMG_8786.svg";
import img15 from "../images/PortfolioSVGs/IMG_8788.svg";
import img16 from "../images/PortfolioSVGs/IMG_8789.svg";
import img17 from "../images/PortfolioSVGs/IMG_8792.svg";
import img18 from "../images/PortfolioSVGs/IMG_8808.svg";
import img19 from "../images/PortfolioSVGs/IMG_8809.svg";
import img20 from "../images/PortfolioSVGs/IMG_8888.svg";
import img21 from "../images/PortfolioSVGs/IMG_8889.svg";
import img22 from "../images/PortfolioSVGs/IMG_8954.svg";
import img23 from "../images/PortfolioSVGs/IMG_8955.svg";
import img24 from "../images/PortfolioSVGs/IMG_8956.svg";
import img25 from "../images/PortfolioSVGs/IMG_9073.svg";
import img26 from "../images/PortfolioSVGs/IMG_9251.svg";
import img27 from "../images/PortfolioSVGs/IMG_9254.svg";
import img28 from "../images/PortfolioSVGs/IMG_9258.svg";
import img29 from "../images/PortfolioSVGs/IMG_9265.svg";
import img30 from "../images/PortfolioSVGs/IMG_9266.svg";
import img31 from "../images/PortfolioSVGs/IMG_9269.svg";
import img32 from "../images/PortfolioSVGs/IMG_9271.svg";
import img33 from "../images/PortfolioSVGs/IMG_9273.svg";
import img34 from "../images/PortfolioSVGs/IMG_9274.svg";
import img35 from "../images/PortfolioSVGs/IMG_9279.svg";
import img36 from "../images/PortfolioSVGs/IMG_9288.svg";
import img37 from "../images/PortfolioSVGs/IMG_9291.svg";
import img38 from "../images/PortfolioSVGs/IMG_9434.svg";
import img39 from "../images/PortfolioSVGs/IMG_9449.svg";
import img40 from "../images/PortfolioSVGs/IMG_9452.svg";
import img41 from "../images/PortfolioSVGs/IMG_9523.svg";
import img42 from "../images/PortfolioSVGs/IMG_9525.svg";
import img43 from "../images/PortfolioSVGs/IMG_9734.svg";
import img44 from "../images/PortfolioSVGs/IMG_9735.svg";
import { useNavigate } from "react-router-dom";
import Header from "./Header";
import Footer from "./Footer";

const PhotosSection = () => {
  const navigate = useNavigate();
  const portfolioImages = [
    img1,
    img2,
    img3,
    img4,
    img5,
    img6,
    img7,
    img8,
    img9,
    img10,
    img11,
    img12,
    img13,
    img14,
    img15,
    img16,
    img17,
    img18,
    img19,
    img20,
    img21,
    img22,
    img23,
    img24,
    img25,
    img26,
    img27,
    img28,
    img29,
    img30,
    img31,
    img32,
    img33,
    img34,
    img35,
    img36,
    img37,
    img38,
    img39,
    img40,
    img41,
    img42,
    img43,
    img44,
  ];

  return (
    <>
      <Header />
      <section className="py-8 sm:py-12 lg:py-16 px-4 sm:px-6 lg:px-20 bg-white mt-20">
        <div className="max-w-7xl mx-auto">
          {/* Back button */}
          <div className="mb-6">
            <button
              onClick={() => navigate("/")}
              className="flex items-center gap-2 text-violet-800 hover:text-violet-900 font-medium transition-colors"
            >
              <svg
                className="w-5 h-5"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M15 19l-7-7 7-7"
                />
              </svg>
              Back to Home
            </button>
          </div>

          <div className="text-center mb-8 sm:mb-12">
            <h2 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
              More of our Works
            </h2>
            <p className="text-gray-600 text-sm sm:text-base">
              Our Featured Creations.
            </p>
          </div>

          {/* 3-column grid layout */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {portfolioImages.map((image, index) => (
              <div key={index} className="group cursor-pointer">
                <div className="relative overflow-hidden rounded-lg shadow-md hover:shadow-lg transition-all duration-500 ease-out transform hover:scale-[1.02]">
                  <img
                    src={image}
                    alt={`Portfolio ${index + 1}`}
                    className="w-full h-96 object-cover transition-all duration-500 ease-out group-hover:brightness-105"
                    loading="lazy"
                    decoding="async"
                    style={{
                      willChange: "transform, filter",
                      backfaceVisibility: "hidden",
                      transform: "translateZ(0)",
                    }}
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500 ease-out" />
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>
      <Footer />
    </>
  );
};

export default PhotosSection;
