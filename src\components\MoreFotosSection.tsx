import img1 from "../images/PortfolioSVGs/IMG_0033.svg";
import img2 from "../images/PortfolioSVGs/IMG_0037.svg";
import img3 from "../images/PortfolioSVGs/IMG_0038.svg";
import img4 from "../images/PortfolioSVGs/IMG_0039.svg";
import img5 from "../images/PortfolioSVGs/IMG_0069.svg";
import img6 from "../images/PortfolioSVGs/IMG_0070.svg";
import img7 from "../images/PortfolioSVGs/IMG_0072.svg";
import img8 from "../images/PortfolioSVGs/IMG_0080.svg";
import img9 from "../images/PortfolioSVGs/IMG_0081.svg";
import img10 from "../images/PortfolioSVGs/IMG_0082.svg";
import img11 from "../images/PortfolioSVGs/IMG_0217.svg";
import img12 from "../images/PortfolioSVGs/IMG_8784.svg";
import img13 from "../images/PortfolioSVGs/IMG_8785.svg";
import img14 from "../images/PortfolioSVGs/IMG_8786.svg";
import img15 from "../images/PortfolioSVGs/IMG_8788.svg";
import img16 from "../images/PortfolioSVGs/IMG_8789.svg";
import img17 from "../images/PortfolioSVGs/IMG_8792.svg";
import img18 from "../images/PortfolioSVGs/IMG_8808.svg";
import img19 from "../images/PortfolioSVGs/IMG_8809.svg";
import img20 from "../images/PortfolioSVGs/IMG_8888.svg";
import img21 from "../images/PortfolioSVGs/IMG_8889.svg";
import img22 from "../images/PortfolioSVGs/IMG_8954.svg";
import img23 from "../images/PortfolioSVGs/IMG_8955.svg";
import img24 from "../images/PortfolioSVGs/IMG_8956.svg";
import img25 from "../images/PortfolioSVGs/IMG_9073.svg";
import img26 from "../images/PortfolioSVGs/IMG_9251.svg";
import img27 from "../images/PortfolioSVGs/IMG_9254.svg";
import img28 from "../images/PortfolioSVGs/IMG_9258.svg";
import img29 from "../images/PortfolioSVGs/IMG_9265.svg";
import img30 from "../images/PortfolioSVGs/IMG_9266.svg";
import img31 from "../images/PortfolioSVGs/IMG_9269.svg";
import img32 from "../images/PortfolioSVGs/IMG_9271.svg";
import img33 from "../images/PortfolioSVGs/IMG_9273.svg";
import img34 from "../images/PortfolioSVGs/IMG_9274.svg";
import img35 from "../images/PortfolioSVGs/IMG_9279.svg";
import img36 from "../images/PortfolioSVGs/IMG_9288.svg";
import img37 from "../images/PortfolioSVGs/IMG_9291.svg";
import img38 from "../images/PortfolioSVGs/IMG_9434.svg";
import img39 from "../images/PortfolioSVGs/IMG_9449.svg";
import img40 from "../images/PortfolioSVGs/IMG_9452.svg";
import img41 from "../images/PortfolioSVGs/IMG_9523.svg";
import img42 from "../images/PortfolioSVGs/IMG_9525.svg";
import img43 from "../images/PortfolioSVGs/IMG_9734.svg";
import img44 from "../images/PortfolioSVGs/IMG_9735.svg";
import { Link } from "react-router-dom";
import { useState, useEffect, useRef, useCallback } from "react";
import Header from "./Header";
import Footer from "./Footer";

const OptimizedImage = ({
  src,
  alt,
  index,
}: {
  src: string;
  alt: string;
  index: number;
}) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [isInView, setIsInView] = useState(false);
  const [hasError, setHasError] = useState(false);
  const imgRef = useRef(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsInView(true);
          observer.unobserve(entry.target);
        }
      },
      {
        rootMargin: "50px", // Start loading when image is 50px away from viewport
        threshold: 0.1,
      }
    );

    if (imgRef.current) {
      observer.observe(imgRef.current);
    }

    return () => {
      if (imgRef.current) {
        observer.unobserve(imgRef.current);
      }
    };
  }, []);

  const handleImageLoad = useCallback(() => {
    setIsLoaded(true);
  }, []);

  const handleImageError = useCallback(() => {
    setHasError(true);
    setIsLoaded(true);
  }, []);

  // For SVG files, set a timeout to ensure they load
  useEffect(() => {
    if (isInView) {
      const timer = setTimeout(() => {
        setIsLoaded(true);
      }, 300); // Short timeout for SVGs

      return () => clearTimeout(timer);
    }
  }, [isInView]);

  return (
    <div ref={imgRef} className="group cursor-pointer">
      <div className="relative overflow-hidden rounded-lg shadow-md hover:shadow-lg transition-all duration-500 ease-out transform hover:scale-[1.02]">
        {!isLoaded && (
          <div className="w-full h-96 bg-gray-200 rounded-lg flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-violet-800"></div>
          </div>
        )}

        {hasError && isLoaded && (
          <div className="w-full h-96 bg-gray-100 rounded-lg flex items-center justify-center">
            <div className="text-gray-400 text-center">
              <svg
                className="w-12 h-12 mx-auto mb-2"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path
                  fillRule="evenodd"
                  d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z"
                  clipRule="evenodd"
                />
              </svg>
              <p className="text-sm">Failed to load</p>
            </div>
          </div>
        )}

        {isInView && !hasError && (
          <img
            src={src}
            alt={alt}
            className={`w-full h-96 object-cover transition-all duration-500 ease-out group-hover:brightness-105 ${
              isLoaded ? "opacity-100" : "opacity-0"
            }`}
            onLoad={handleImageLoad}
            onError={handleImageError}
            loading="lazy"
            decoding="async"
          />
        )}

        {isLoaded && !hasError && (
          <div className="absolute inset-0 bg-gradient-to-t from-black/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500 ease-out" />
        )}
      </div>
    </div>
  );
};

const PhotosSection = () => {
  const [imagesLoaded, setImagesLoaded] = useState(0);
  const portfolioImages = [
    img1,
    img2,
    img3,
    img4,
    img5,
    img6,
    img7,
    img8,
    img9,
    img10,
    img11,
    img12,
    img13,
    img14,
    img15,
    img16,
    img17,
    img18,
    img19,
    img20,
    img21,
    img22,
    img23,
    img24,
    img25,
    img26,
    img27,
    img28,
    img29,
    img30,
    img31,
    img32,
    img33,
    img34,
    img35,
    img36,
    img37,
    img38,
    img39,
    img40,
    img41,
    img42,
    img43,
    img44,
  ];

  // const portfolioImages = Array.from(
  //   { length: 44 },
  //   (_, i) => `https://picsum.photos/600/400?random=${i + 1}`
  // );

  const handleImageLoad = useCallback(() => {
    setImagesLoaded((prev) => prev + 1);
  }, []);

  return (
    <div className="py-10 sm:py-8 lg:py-14 px-4 sm:px-6 lg:px-20 bg-white">
      <Header />
      <div className="pt-10">
        <Link
          to="/"
          className="flex items-center py-3 gap-2 text-violet-950 hover:text-violet-900 font-medium transition-colors bg-white px-4  rounded-lg shadow-md border border-gray-200 inline-flex"
        >
          <svg
            className="w-5 h-5"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M15 19l-7-7 7-7"
            />
          </svg>
          Back Home
        </Link>
      </div>

      <section className=" pb-8 sm:pb-12 lg:pb-16 px-4 sm:px-6 lg:px-20 bg-white mt-10">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-8 sm:mb-12">
            <h2 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
              More of our Works
            </h2>
            <p className="text-gray-600 text-sm sm:text-base pb-10">
              Our Featured Creations.
            </p>

            {/* Loading progress indicator
            {imagesLoaded < portfolioImages.length && (
              <div className="mt-4">
                <div className="text-sm text-gray-500 mb-2">
                  Loading images... ({imagesLoaded}/{portfolioImages.length})
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2 max-w-md mx-auto">
                  <div
                    className="bg-violet-800 h-2 rounded-full transition-all duration-300"
                    style={{
                      width: `${
                        (imagesLoaded / portfolioImages.length) * 100
                      }%`,
                    }}
                  ></div>
                </div>
              </div>
            )}
          </div> */}

            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
              {portfolioImages.map((image, index) => (
                <OptimizedImage
                  key={index}
                  src={image}
                  alt={`Portfolio ${index + 1}`}
                  index={index}
                />
              ))}
            </div>
          </div>
        </div>
      </section>
      <Footer />
    </div>
  );
};

export default PhotosSection;
