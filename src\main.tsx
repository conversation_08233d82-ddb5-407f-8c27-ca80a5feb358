import React from "react";
import ReactDOM from "react-dom/client";
import { <PERSON><PERSON>er<PERSON>out<PERSON> } from "react-router-dom";
import Index from "./pages/Index";
import "./index.css";

// Test component to verify everything is working
const TestComponent = () => (
  <div className="p-8 bg-red-500 text-white">
    <h1 className="text-4xl font-bold">
      🎉 React + Vite + Tailwind is working!
    </h1>
    <p className="mt-4">
      If you can see this red box with white text, everything is configured
      correctly.
    </p>
  </div>
);

ReactDOM.createRoot(document.getElementById("root")!).render(
  <React.StrictMode>
    <BrowserRouter>
      <TestComponent />
      <Index />
    </BrowserRouter>
  </React.StrictMode>
);
