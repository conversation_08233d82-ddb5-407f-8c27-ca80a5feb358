import React from "react";
import ReactDOM from "react-dom/client";
import { BrowserRouter, Routes, Route, useLocation } from "react-router-dom";
import Index from "./pages/Index";
import PhotosSection from "./components/MoreFotosSection";
import "./index.css";

function App() {
  const location = useLocation();

  return (
    <Routes>
      <Route path="/" element={<Index key="index" />} />
      <Route path="/photos" element={<PhotosSection key="photos" />} />
    </Routes>
  );
}

ReactDOM.createRoot(document.getElementById("root")!).render(
  <React.StrictMode>
    <BrowserRouter>
      <App />
    </BrowserRouter>
  </React.StrictMode>
);
