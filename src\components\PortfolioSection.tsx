import React from "react";
import img1 from "../images/PortfolioSection/Rectangle 17.svg";
import img2 from "../images/PortfolioSection/Rectangle 18.svg";
import img3 from "../images/PortfolioSection/Rectangle 19.svg";
import img4 from "../images/PortfolioSection/Rectangle 20.svg";
import img5 from "../images/PortfolioSection/Rectangle 21.svg";
import img6 from "../images/PortfolioSection/Rectangle 22.svg";
import img7 from "../images/PortfolioSection/Rectangle 23.svg";
import img8 from "../images/PortfolioSection/Rectangle 24.svg";
import img9 from "../images/PortfolioSection/Rectangle 25.svg";

const PortfolioSection = () => {
  const portfolioImages = [
    img1,
    img2,
    img3,
    img4,
    img5,
    img6,
    img7,
    img8,
    img9,
  ];

  return (
    <section className="py-16 px-20 bg-white">
      <div className="max-w-7xl mx-auto">
        <div className="text-center mb-12">
          <h2 className="text-4xl font-bold text-gray-900 mb-4">
            Our Awesome Works
          </h2>
          <p className="text-gray-600">Our Pride of existence.</p>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {portfolioImages.map((image, index) => (
            <div key={index} className="group overflow-hidden rounded-lg portfolio_containter">
              <img
                //key={index + 1}
                src={image}
                alt={`Portfolio ${index + 1}`}
                className="portfolio_Image scale-105 w-auto h-80 object-cover group-hover:scale-105 transition-transform duration-300"
              />
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default PortfolioSection;
