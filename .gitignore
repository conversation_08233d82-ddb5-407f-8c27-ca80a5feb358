# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

node_modules
/node_modules
dist
dist-ssr
*.local

# Editor directories and files
.vscode/*
!.vscode/extensions.json
extensions.json
*extensions.json
*.extensions.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
generated/prisma/
node_modules
# Keep environment variables out of version control
.env
bash.exe.stackdump
*bash.exe.stackdump
.rest
*.rest
Examinator.rest
src/backend/tests/Examinator.rest
